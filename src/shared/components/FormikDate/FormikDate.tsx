import React from 'react';
import { Container, DateInput, Input, Label } from '@ghq-abi/design-system-v2';
import { ErrorMessage, useField } from 'formik';

import { cn } from '~/shared/utils/cn';

import { FormikDateProps } from './types';

export function FormikDate({
  name,
  label,
  placeholder,
  onChange,
  value,
  yearOnly = false,
  ...props
}: FormikDateProps) {
  const [field, meta, helpers] = useField(name);
  const hasError = meta.touched && meta.error;

  const currentYear = new Date().getFullYear();

  if (yearOnly) {
    const currentYearValue = field.value ? field.value.split('-')[0] : '';

    const handleYearChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const selectedYear = event.target.value;
      const dateValue = selectedYear ? `${selectedYear}-01-01` : '';
      void helpers.setValue(dateValue);
      if (onChange) {
        onChange({ target: { value: dateValue } } as any);
      }
    };

    return (
      <Container className="grid w-full items-center">
        <Label className="pb-1" htmlFor={name}>
          {label}
        </Label>
        <Input
          id={name}
          name={name}
          type="number"
          value={currentYearValue}
          onChange={handleYearChange}
          placeholder={placeholder || 'YYYY'}
          min={currentYear - 10}
          max={currentYear + 10}
          className={cn(
            'bg-gray-50 block text-neutral-500 border-neutral-200',
            hasError ? 'border-red-500 focus:border-red-500' : '',
          )}
          {...props}
        />
        <Container className="min-h-[20px]">
          <ErrorMessage
            name={name}
            component="div"
            className="text-red-500 text-sm"
          />
        </Container>
      </Container>
    );
  }

  const dateValue = field.value ? new Date(field.value) : null;

  const handleDateChange = (date: Date | null) => {
    const dateValue = date ? date.toISOString().split('T')[0] : '';
    void helpers.setValue(dateValue);
    if (onChange) {
      onChange({ target: { value: dateValue } } as any);
    }
  };

  return (
    <Container className="grid w-full items-center">
      <Label className="pb-1" htmlFor={name}>
        {label}
      </Label>
      <DateInput
        value={dateValue}
        onChange={handleDateChange}
        placeholder={placeholder}
        className={cn(
          'bg-gray-50 block text-neutral-500 border-neutral-200',
          hasError ? 'border-red-500 focus:border-red-500' : '',
        )}
        {...props}
      />
      <Container className="min-h-[20px]">
        <ErrorMessage
          name={name}
          component="div"
          className="text-red-500 text-sm"
        />
      </Container>
    </Container>
  );
}
