import React from 'react';
import { Container, DateInput, Label, Select } from '@ghq-abi/design-system-v2';
import { ErrorMessage, useField } from 'formik';

import { cn } from '~/shared/utils/cn';

import { FormikDateProps } from './types';

export function FormikDate({
  name,
  label,
  placeholder,
  onChange,
  value,
  yearOnly = false,
  ...props
}: FormikDateProps) {
  const [field, meta, helpers] = useField(name);
  const hasError = meta.touched && meta.error;

  // Generate year options (current year ± 10 years)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 21 }, (_, i) => {
    const year = currentYear - 10 + i;
    return { value: year.toString(), label: year.toString() };
  });

  if (yearOnly) {
    // Extract year from the current value (format: YYYY-MM-DD)
    const currentYearValue = field.value ? field.value.split('-')[0] : '';

    const handleYearChange = (selectedYear: string) => {
      // Set to January 1st of the selected year
      const dateValue = selectedYear ? `${selectedYear}-01-01` : '';
      void helpers.setValue(dateValue);
      if (onChange) {
        onChange({ target: { value: dateValue } } as any);
      }
    };

    return (
      <Container className="grid w-full items-center">
        <Label className="pb-1" htmlFor={name}>
          {label}
        </Label>
        <Select.Root
          name={name}
          value={currentYearValue}
          onValueChange={handleYearChange}
        >
          <Select.Trigger
            className={cn(
              'bg-gray-50',
              hasError ? 'border-red-500 focus:border-red-500' : '',
            )}
          >
            <Select.Value placeholder={placeholder || 'Select year'} />
          </Select.Trigger>
          <Select.Content>
            <Select.Group>
              {yearOptions.map(option => (
                <Select.Item key={option.value} value={option.value}>
                  {option.label}
                </Select.Item>
              ))}
            </Select.Group>
          </Select.Content>
        </Select.Root>
        <Container className="min-h-[20px]">
          <ErrorMessage
            name={name}
            component="div"
            className="text-red-500 text-sm"
          />
        </Container>
      </Container>
    );
  }

  // Original date picker functionality
  const dateValue = field.value ? new Date(field.value) : null;

  const handleDateChange = (date: Date | null) => {
    const dateValue = date ? date.toISOString().split('T')[0] : '';
    void helpers.setValue(dateValue);
    if (onChange) {
      onChange({ target: { value: dateValue } } as any);
    }
  };

  return (
    <Container className="grid w-full items-center">
      <Label className="pb-1" htmlFor={name}>
        {label}
      </Label>
      <DateInput
        value={dateValue}
        onChange={handleDateChange}
        placeholder={placeholder}
        className={cn(
          'bg-gray-50 block text-neutral-500 border-neutral-200',
          hasError ? 'border-red-500 focus:border-red-500' : '',
        )}
        {...props}
      />
      <Container className="min-h-[20px]">
        <ErrorMessage
          name={name}
          component="div"
          className="text-red-500 text-sm"
        />
      </Container>
    </Container>
  );
}
