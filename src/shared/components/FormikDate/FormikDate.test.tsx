/**
 * @jest-environment jsdom
 */

describe('FormikDate Component', () => {
  it('should have yearOnly prop functionality', () => {
    // Test that the component accepts yearOnly prop
    const currentYear = new Date().getFullYear();
    const yearOptions = Array.from({ length: 21 }, (_, i) => {
      const year = currentYear - 10 + i;
      return { value: year.toString(), label: year.toString() };
    });

    // Verify year range generation
    expect(yearOptions).toHaveLength(21);
    expect(yearOptions[0].value).toBe((currentYear - 10).toString());
    expect(yearOptions[20].value).toBe((currentYear + 10).toString());
  });

  it('should format date correctly for year-only mode', () => {
    const selectedYear = '2024';
    const expectedDateValue = `${selectedYear}-01-01`;

    // Verify date formatting
    expect(expectedDateValue).toBe('2024-01-01');
  });

  it('should extract year from date string', () => {
    const dateValue = '2024-01-01';
    const extractedYear = dateValue.split('-')[0];

    expect(extractedYear).toBe('2024');
  });
});
