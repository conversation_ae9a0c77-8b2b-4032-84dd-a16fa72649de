import { Container, Label, Select } from '@ghq-abi/design-system-v2';
import { ErrorMessage, useField } from 'formik';

import { cn } from '~/shared/utils/cn';

export function FormikSelect({
  name,
  label,
  options,
  onChange,
  placeholder,
  value,
  ...props
}: FormikSelectProps) {
  const [_, meta] = useField(name);
  const hasError = meta.touched && meta.error;

  const handleValueChange = (val: string) => {
    if (onChange) {
      onChange({
        target: {
          name,
          value: val,
        },
      } as React.ChangeEvent<any>);
    }
  };

  return (
    <Container className="grid w-full items-center">
      <Label className="pb-1" htmlFor={name}>
        {label}
      </Label>
      <Select.Root
        name={name}
        value={value}
        onValueChange={handleValueChange}
        {...props}
      >
        <Select.Trigger
          className={cn(
            'bg-gray-50',
            hasError ? 'border-red-500 focus:border-red-500' : '',
          )}
          {...props}
        >
          <Select.Value placeholder={placeholder} />
        </Select.Trigger>
        <Select.Content id={name} onChange={onChange} className="z-[9999]">
          <Select.Group onChange={onChange}>
            <Select.Label>{label}</Select.Label>
            {options.map((opt: Option) => (
              <Select.Item key={opt.value} value={opt.value}>
                {opt.label}
              </Select.Item>
            ))}
          </Select.Group>
        </Select.Content>
      </Select.Root>
      <Container className="min-h-[20px]">
        <ErrorMessage
          name={name}
          component="div"
          className="text-red-500 text-sm"
        />
      </Container>
    </Container>
  );
}
